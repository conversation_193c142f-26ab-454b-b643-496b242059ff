// The module 'vscode' contains the VS Code extensibility API
// Import the module and reference it with the alias vscode in your code below
import * as vscode from 'vscode';

// ChatViewProvider class that implements WebviewViewProvider
class ChatViewProvider implements vscode.WebviewViewProvider {
	public static readonly viewType = 'ai-chat-view';

	constructor(private readonly _extensionUri: vscode.Uri) {}

	public resolveWebviewView(
		webviewView: vscode.WebviewView,
		context: vscode.WebviewViewResolveContext,
		_token: vscode.CancellationToken,
	) {
		console.log('resolveWebviewView called for:', ChatViewProvider.viewType);

		webviewView.webview.options = {
			// Allow scripts in the webview
			enableScripts: true,
			localResourceRoots: [
				this._extensionUri
			]
		};

		webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);
		console.log('WebView HTML set successfully');
	}

	private _getHtmlForWebview(webview: vscode.Webview) {
		return `<!DOCTYPE html>
			<html lang="en">
			<head>
				<meta charset="UTF-8">
				<meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src ${webview.cspSource} 'unsafe-inline';">
				<meta name="viewport" content="width=device-width, initial-scale=1.0">
				<title>AI Chat</title>
				<style>
					body {
						font-family: var(--vscode-font-family);
						color: var(--vscode-foreground);
						background-color: var(--vscode-editor-background);
						padding: 20px;
					}
					.test-message {
						background-color: var(--vscode-editor-selectionBackground);
						padding: 10px;
						border-radius: 5px;
						margin: 10px 0;
					}
				</style>
			</head>
			<body>
				<h2>AI Chat View</h2>
				<div class="test-message">
					✅ WebView 已成功加载！
				</div>
				<div class="test-message">
					🎉 resolveWebviewView 方法已被调用
				</div>
				<p>如果你能看到这个消息，说明WebView View Provider已经正确注册和工作了。</p>
			</body>
			</html>`;
}
}

// This method is called when your extension is activated
// Your extension is activated the very first time the command is executed
export function activate(context: vscode.ExtensionContext) {

	// Use the console to output diagnostic information (console.log) and errors (console.error)
	// This line of code will only be executed once when your extension is activated
	console.log('Congratulations, your extension "ai-extension-demo" is now active!');

	// Register the webview view provider
	const chatViewProvider = new ChatViewProvider(context.extensionUri);

	// Try registering with minimal options first
	try {
		const disposableViewProvider = vscode.window.registerWebviewViewProvider(
			'ai-chat-view', // Use string directly instead of static property
			chatViewProvider
		);
		context.subscriptions.push(disposableViewProvider);
		console.log('WebviewViewProvider registered successfully for: ai-chat-view');
	} catch (error) {
		console.error('Failed to register WebviewViewProvider:', error);
	}

	// The command has been defined in the package.json file
	// Now provide the implementation of the command with registerCommand
	// The commandId parameter must match the command field in package.json
	const disposable = vscode.commands.registerCommand('ai-extension-demo.helloWorld', () => {
		// The code you place here will be executed every time your command is executed
		// Display a message box to the user
		vscode.window.showInformationMessage('Hello World from ai_extension_demo!');
	});

	context.subscriptions.push(disposable);
}

// This method is called when your extension is deactivated
export function deactivate() {}
